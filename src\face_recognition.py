import cv2
import os
import numpy as np
import face_recognition
import pickle
import threading
import time

class FaceRecognizer:
    def __init__(self, data_dir="data/students", model_path="models/encodings.pkl",
                 use_opencv_dnn=False, default_tolerance=0.6, default_min_confidence=0.5):  # Relaxed for better recognition
        self.data_dir = data_dir
        self.model_path = model_path
        self.use_opencv_dnn = use_opencv_dnn
        self.encodings = []
        self.names = []
        self.default_tolerance = default_tolerance
        self.default_min_confidence = default_min_confidence
        self.recognition_cooldown = {}
        self.cooldown_time = 3  # seconds
        
        # Create directories if they don't exist
        os.makedirs(os.path.dirname(model_path), exist_ok=True)
        os.makedirs(data_dir, exist_ok=True)
        
        # Initialize OpenCV DNN models if requested
        if use_opencv_dnn:
            self._init_opencv_models()
        
        # Load existing model if available
        if os.path.exists(model_path):
            self.load_encodings()
    
    def _init_opencv_models(self):
        """Initialize OpenCV DNN face detection and recognition models"""
        try:
            # Initialize face detector
            self.face_detector = cv2.FaceDetectorYN.create(
                "models/face_detection_yunet.onnx",
                "",
                (320, 320),
                0.9,
                0.3,
                5000
            )
            
            # Initialize face recognizer
            self.face_recognizer_dnn = cv2.FaceRecognizerSF.create(
                "models/face_recognition_sface.onnx",
                ""
            )
            print("✓ OpenCV DNN models loaded successfully")
        except Exception as e:
            print(f"✗ Failed to load OpenCV DNN models: {e}")
            print("Falling back to face_recognition library")
            self.use_opencv_dnn = False
    
    def load_encodings(self):
        try:
            with open(self.model_path, "rb") as f:
                data = pickle.load(f)
                self.encodings = data["encodings"]
                self.names = data["names"]
            print(f"✓ Loaded {len(self.encodings)} face encodings")
        except FileNotFoundError:
            print("No existing encodings found. Starting with empty model.")
            self.encodings = []
            self.names = []
        except Exception as e:
            print(f"✗ Error loading encodings: {e}")
            self.encodings = []
            self.names = []
    
    def save_encodings(self):
        data = {"encodings": self.encodings, "names": self.names}
        with open(self.model_path, "wb") as f:
            pickle.dump(data, f)
    
    def identify_face(self, frame, tolerance=None, min_confidence=None):
        """Optimized face identification"""
        tolerance = tolerance or self.default_tolerance
        min_confidence = min_confidence or self.default_min_confidence
        
        if self.use_opencv_dnn and hasattr(self, 'face_detector'):
            return self._identify_face_opencv(frame, tolerance, min_confidence)
        else:
            return self._identify_face_library(frame, tolerance, min_confidence)
    
    def _identify_face_opencv(self, frame, tolerance, min_confidence):
        """Face identification using OpenCV DNN models"""
        height, width, _ = frame.shape
        self.face_detector.setInputSize((width, height))
        _, faces = self.face_detector.detect(frame)
        
        results = []
        if faces is not None:
            for face in faces:
                x, y, w, h = face[0:4].astype(int)
                
                # Extract face feature
                face_align = self.face_recognizer_dnn.alignCrop(frame, face)
                feature = self.face_recognizer_dnn.feature(face_align)
                
                # Find best match with lower threshold
                best_match = None
                best_score = 0
                
                for i, (student_id, db_feature) in enumerate(zip(self.names, self.encodings)):
                    score = self.face_recognizer_dnn.match(feature, db_feature, cv2.FaceRecognizerSF.FR_COSINE)
                    if score > best_score and score > 0.6:  # Tightened from 0.5
                        best_score = score
                        best_match = student_id
                
                # Convert to standard format (top, right, bottom, left)
                box = (y, x + w, y + h, x)
                name = best_match if best_match else "Unknown"
                results.append((name, box, best_score))
        
        return results
    
    def _identify_face_library(self, frame, tolerance, min_confidence):
        """Face identification using face_recognition library (improved)"""
        # Resize frame for speed but keep reasonable quality
        small_frame = cv2.resize(frame, (0, 0), fx=0.5, fy=0.5)  # Changed from 0.25
        rgb = cv2.cvtColor(small_frame, cv2.COLOR_BGR2RGB)
        
        boxes = face_recognition.face_locations(rgb, model="hog")
        if len(boxes) == 0:
            return []
        
        encodings = face_recognition.face_encodings(rgb, boxes, num_jitters=1)
        boxes = [(top*2, right*2, bottom*2, left*2) for top, right, bottom, left in boxes]  # Adjust scaling
        
        results = []
        for encoding, box in zip(encodings, boxes):
            if len(self.encodings) > 0:
                face_distances = face_recognition.face_distance(self.encodings, encoding)
                best_match_index = np.argmin(face_distances)
                confidence = 1 - face_distances[best_match_index]
                
                # More lenient matching
                if face_distances[best_match_index] <= tolerance:  # Use distance directly
                    name = self.names[best_match_index]
                    results.append((name, box, confidence))
                else:
                    results.append(("Unknown", box, confidence))
            else:
                results.append(("Unknown", box, 0))
        
        return results
    
    def register_new_student(self, student_id, images):
        """Register a new student with multiple face images"""
        student_dir = os.path.join(self.data_dir, student_id)
        os.makedirs(student_dir, exist_ok=True)
        
        success_count = 0
        for i, image in enumerate(images):
            image_path = os.path.join(student_dir, f"{student_id}_{i}.jpg")
            cv2.imwrite(image_path, image)
            
            if self.use_opencv_dnn and hasattr(self, 'face_detector'):
                success = self._add_face_opencv(image, student_id)
            else:
                success = self._add_face_library(image, student_id)
            
            if success:
                success_count += 1
        
        if success_count > 0:
            self.save_encodings()
            return True
        return False
    
    def _add_face_opencv(self, image, student_id):
        """Add face using OpenCV DNN"""
        height, width, _ = image.shape
        self.face_detector.setInputSize((width, height))
        _, faces = self.face_detector.detect(image)
        
        if faces is not None and len(faces) > 0:
            face = faces[0]
            face_align = self.face_recognizer_dnn.alignCrop(image, face)
            feature = self.face_recognizer_dnn.feature(face_align)
            
            self.encodings.append(feature)
            self.names.append(student_id)
            return True
        return False
    
    def _add_face_library(self, image, student_id):
        """Add face using face_recognition library with multiple encodings"""
        rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        boxes = face_recognition.face_locations(rgb, model="hog")
        
        if len(boxes) > 0:
            # Increase jitters for better encoding variety
            encodings = face_recognition.face_encodings(rgb, boxes, num_jitters=5)  # Increased from 3
            for encoding in encodings:
                self.encodings.append(encoding)
                self.names.append(student_id)
            return True
        return False
    
    def train_from_directory(self):
        """Train model from existing directory structure"""
        print("Training face recognition model...")
        self.encodings = []
        self.names = []
        
        for person_id in os.listdir(self.data_dir):
            person_dir = os.path.join(self.data_dir, person_id)
            if not os.path.isdir(person_dir):
                continue
            
            print(f"Processing images for {person_id}")
            for image_name in os.listdir(person_dir):
                if not image_name.lower().endswith(('.png', '.jpg', '.jpeg')):
                    continue
                
                image_path = os.path.join(person_dir, image_name)
                image = cv2.imread(image_path)
                if image is not None:
                    if self.use_opencv_dnn:
                        self._add_face_opencv(image, person_id)
                    else:
                        self._add_face_library(image, person_id)
        
        self.save_encodings()
        print(f"✓ Training complete. {len(self.encodings)} face encodings saved.")



















