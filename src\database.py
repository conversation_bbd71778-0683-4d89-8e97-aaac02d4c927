import sqlite3
import os
import pandas as pd
from datetime import datetime

class Database:
    def __init__(self, db_path="data/attendance.db"):
        # Ensure directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        try:
            self.conn = sqlite3.connect(db_path)
            self.cursor = self.conn.cursor()
            self.create_tables()
        except sqlite3.Error as e:
            print(f"Database error: {e}")
            raise
    
    def create_tables(self):
        # Students table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS students (
            student_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            course TEXT,
            email TEXT,
            registration_date TEXT
        )
        ''')
        
        # Attendance table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            student_id TEXT,
            date TEXT,
            time TEXT,
            status TEXT,
            FOREIGN KEY (student_id) REFERENCES students (student_id)
        )
        ''')
        
        self.conn.commit()
    
    def add_student(self, student_id, name, course="", email=""):
        try:
            registration_date = datetime.now().strftime("%Y-%m-%d")
            self.cursor.execute(
                "INSERT INTO students VALUES (?, ?, ?, ?, ?)",
                (student_id, name, course, email, registration_date)
            )
            self.conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False  # Student ID already exists
    
    def update_student(self, student_id, name=None, course=None, email=None):
        """Update an existing student's information"""
        # Get current values to only update what's provided
        current = self.get_student(student_id)
        if not current:
            return False  # Student doesn't exist
            
        # Use provided values or keep current ones
        new_name = name if name is not None else current[1]
        new_course = course if course is not None else current[2]
        new_email = email if email is not None else current[3]
        
        self.cursor.execute(
            "UPDATE students SET name=?, course=?, email=? WHERE student_id=?",
            (new_name, new_course, new_email, student_id)
        )
        self.conn.commit()
        return True
    
    def get_student(self, student_id):
        self.cursor.execute("SELECT * FROM students WHERE student_id=?", (student_id,))
        return self.cursor.fetchone()
    
    def get_all_students(self):
        self.cursor.execute("SELECT * FROM students")
        return self.cursor.fetchall()
    
    def mark_attendance(self, student_id, status="present"):
        now = datetime.now()
        date = now.strftime("%Y-%m-%d")
        time = now.strftime("%H:%M:%S")
        
        # Check if attendance already marked today
        self.cursor.execute(
            "SELECT * FROM attendance WHERE student_id=? AND date=?", 
            (student_id, date)
        )
        if self.cursor.fetchone():
            return False  # Already marked
        
        self.cursor.execute(
            "INSERT INTO attendance (student_id, date, time, status) VALUES (?, ?, ?, ?)",
            (student_id, date, time, status)
        )
        self.conn.commit()
        return True
    
    def get_attendance_report(self, date=None):
        if date:
            self.cursor.execute(
                """SELECT s.student_id, s.name, a.time, a.status 
                   FROM attendance a JOIN students s 
                   ON a.student_id = s.student_id 
                   WHERE a.date=?""", 
                (date,)
            )
        else:
            self.cursor.execute(
                """SELECT s.student_id, s.name, a.date, a.time, a.status 
                   FROM attendance a JOIN students s 
                   ON a.student_id = s.student_id"""
            )
        return self.cursor.fetchall()
    
    def export_attendance(self, output_path, date=None):
        data = self.get_attendance_report(date)
        if date:
            columns = ["Student ID", "Name", "Time", "Status"]
        else:
            columns = ["Student ID", "Name", "Date", "Time", "Status"]
        
        df = pd.DataFrame(data, columns=columns)
        df.to_csv(output_path, index=False)
        return True
    
    def close(self):
        if hasattr(self, 'conn') and self.conn:
            try:
                self.conn.close()
            except sqlite3.Error:
                pass  # Already closed or error on close

