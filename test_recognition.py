#!/usr/bin/env python3
"""
Quick test script to verify face recognition is working with current settings
"""

import cv2
import sys
from src.database import Database
from src.face_recognition import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_recognition():
    """Test face recognition with current settings"""
    print("Testing face recognition...")
    
    # Initialize components
    database = Database()
    face_recognizer = FaceRecognizer(use_opencv_dnn=False)
    
    # Load encodings
    face_recognizer.load_encodings()
    print(f"Loaded {len(face_recognizer.encodings)} face encodings")
    print(f"Known students: {set(face_recognizer.names)}")
    
    # Test with different tolerance values
    tolerances = [0.4, 0.5, 0.6, 0.7]
    
    print("\nStarting camera test...")
    print("Press 'q' to quit, 't' to test different tolerances")
    
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("Error: Could not open camera")
        return
    
    tolerance_index = 2  # Start with 0.6
    current_tolerance = tolerances[tolerance_index]
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Test recognition
        faces = face_recognizer.identify_face(frame, tolerance=current_tolerance, min_confidence=0.4)
        
        # Draw results
        for result in faces:
            if len(result) == 3:
                name, (top, right, bottom, left), confidence = result
            else:
                name, (top, right, bottom, left) = result
                confidence = 0
            
            # Draw rectangle
            color = (0, 255, 0) if name != "Unknown" else (0, 0, 255)
            cv2.rectangle(frame, (left, top), (right, bottom), color, 2)
            
            # Put name and confidence
            label = f"{name} ({confidence:.2f})"
            cv2.putText(frame, label, (left, top - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        # Show tolerance info
        cv2.putText(frame, f"Tolerance: {current_tolerance}", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, "Press 't' to change tolerance, 'q' to quit", (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        cv2.imshow('Face Recognition Test', frame)
        
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
        elif key == ord('t'):
            tolerance_index = (tolerance_index + 1) % len(tolerances)
            current_tolerance = tolerances[tolerance_index]
            print(f"Changed tolerance to: {current_tolerance}")
    
    cap.release()
    cv2.destroyAllWindows()
    print("Test completed")

if __name__ == "__main__":
    test_recognition()
