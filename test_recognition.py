#!/usr/bin/env python3
"""
Quick test script to verify face recognition is working with current settings
"""

import cv2
import sys
from src.database import Database
from src.face_recognition import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_recognition():
    """Test face recognition with current settings"""
    print("Testing face recognition...")
    
    # Initialize components
    database = Database()
    face_recognizer = FaceRecognizer(use_opencv_dnn=False)
    
    # Load encodings
    face_recognizer.load_encodings()
    print(f"Loaded {len(face_recognizer.encodings)} face encodings")
    print(f"Known students: {set(face_recognizer.names)}")
    
    # Use secure fixed settings
    tolerance = 0.45
    min_confidence = 0.65

    print(f"\nUsing secure settings: tolerance={tolerance}, min_confidence={min_confidence}")
    print("Starting camera test...")
    print("Press 'q' to quit")

    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("Error: Could not open camera")
        return
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Test recognition with secure settings
        faces = face_recognizer.identify_face(frame, tolerance=tolerance, min_confidence=min_confidence)

        # Draw results
        for result in faces:
            if len(result) == 3:
                name, (top, right, bottom, left), confidence = result
            else:
                name, (top, right, bottom, left) = result
                confidence = 0

            # Additional security check - verify student exists in database
            if name != "Unknown" and not database.get_student(name):
                name = "Unknown (Not in DB)"

            # Draw rectangle
            color = (0, 255, 0) if name != "Unknown" and "Not in DB" not in name else (0, 0, 255)
            cv2.rectangle(frame, (left, top), (right, bottom), color, 2)

            # Put name and confidence
            label = f"{name} ({confidence:.2f})"
            cv2.putText(frame, label, (left, top - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

            # Log recognition attempts
            if name != "Unknown":
                print(f"✓ Recognized: {name} (confidence: {confidence:.2f})")
            elif confidence > 0.3:
                print(f"⚠ Unknown person (confidence: {confidence:.2f})")

        # Show security info
        cv2.putText(frame, f"Secure Mode: T={tolerance}, C={min_confidence}", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(frame, "Press 'q' to quit", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        cv2.imshow('Secure Face Recognition Test', frame)

        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()
    print("Test completed")

if __name__ == "__main__":
    test_recognition()
