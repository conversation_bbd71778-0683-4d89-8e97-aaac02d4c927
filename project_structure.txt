facial_attendance_system/
├── data/
│   ├── students/          # Student face images organized by ID
│   └── attendance/        # CSV files with attendance records
├── models/                # Trained models
├── src/
│   ├── database.py        # Database operations
│   ├── face_recognition.py # Face detection and recognition
│   ├── attendance.py      # Attendance recording logic
│   └── gui.py             # User interface
└── main.py                # Main application entry point`
