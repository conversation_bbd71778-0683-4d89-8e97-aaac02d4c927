import urllib.request
import os

def download_opencv_models():
    """Download required OpenCV DNN models"""
    models = {
        "face_detection_yunet.onnx": "https://github.com/opencv/opencv_zoo/raw/master/models/face_detection_yunet/face_detection_yunet_2023mar.onnx",
        "face_recognition_sface.onnx": "https://github.com/opencv/opencv_zoo/raw/master/models/face_recognition_sface/face_recognition_sface_2021dec.onnx"
    }
    
    os.makedirs("models", exist_ok=True)
    
    for filename, url in models.items():
        filepath = os.path.join("models", filename)
        if not os.path.exists(filepath):
            print(f"Downloading {filename}...")
            try:
                urllib.request.urlretrieve(url, filepath)
                print(f"✓ Downloaded {filename}")
            except Exception as e:
                print(f"✗ Failed to download {filename}: {e}")
        else:
            print(f"✓ {filename} already exists")

if __name__ == "__main__":
    download_opencv_models()