import os
import csv
from datetime import datetime
import pandas as pd

class AttendanceManager:
    def __init__(self, database, output_dir="data/attendance"):
        self.database = database
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def mark_attendance(self, student_id):
        """Mark attendance for a student"""
        return self.database.mark_attendance(student_id)
    
    def get_today_attendance(self):
        """Get today's attendance records"""
        today = datetime.now().strftime("%Y-%m-%d")
        return self.database.get_attendance_report(today)
    
    def export_today_attendance(self):
        """Export today's attendance to CSV"""
        today = datetime.now().strftime("%Y-%m-%d")
        output_path = os.path.join(self.output_dir, f"attendance_{today}.csv")
        return self.database.export_attendance(output_path, today)
    
    def export_monthly_report(self, year, month):
        """Generate monthly attendance report"""
        # Get all attendance records
        all_records = self.database.get_attendance_report()
        
        # Filter for the specified month
        df = pd.DataFrame(all_records, 
                         columns=["Student ID", "Name", "Date", "Time", "Status"])
        
        # Convert date to datetime
        df["Date"] = pd.to_datetime(df["Date"])
        
        # Filter by year and month
        monthly_data = df[(df["Date"].dt.year == year) & 
                          (df["Date"].dt.month == month)]
        
        # Create pivot table: students vs days
        pivot = pd.pivot_table(
            monthly_data,
            values="Status",
            index=["Student ID", "Name"],
            columns=pd.Grouper(key="Date", freq="D"),
            aggfunc=lambda x: "P" if len(x) > 0 else "A",
            fill_value="A"
        )
        
        # Save to CSV
        output_path = os.path.join(self.output_dir, f"monthly_report_{year}_{month}.csv")
        pivot.to_csv(output_path)
        return output_path

