#!/usr/bin/env python3
"""
Setup script for Facial Recognition Attendance System
"""
import os
import subprocess
import sys

def install_requirements():
    """Install required packages"""
    print("Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install requirements: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    dirs = ["data/students", "data/attendance", "models"]
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"✓ Created directory: {dir_path}")

def download_models():
    """Download OpenCV models"""
    try:
        from download_models import download_opencv_models
        download_opencv_models()
        return True
    except Exception as e:
        print(f"✗ Failed to download models: {e}")
        return False

def main():
    print("Setting up Facial Recognition Attendance System...")
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        print("Setup failed. Please install requirements manually.")
        return
    
    # Download models
    download_models()
    
    print("\n✓ Setup complete!")
    print("Run 'python main.py' to start the application")

if __name__ == "__main__":
    main()