import os
import sys
from src.database import Database
from src.face_recognition import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.attendance import AttendanceManager
from src.gui import AttendanceSystemGUI

def download_models_if_needed():
    """Download OpenCV models if they don't exist"""
    model_files = ["models/face_detection_yunet.onnx", "models/face_recognition_sface.onnx"]
    missing_models = [f for f in model_files if not os.path.exists(f)]
    
    if missing_models:
        print("Missing OpenCV DNN models. Downloading...")
        try:
            from download_models import download_opencv_models
            download_opencv_models()
        except Exception as e:
            print(f"Failed to download models: {e}")
            print("Will use face_recognition library instead")
            return False
    return True

def main():
    print("Starting Facial Recognition Attendance System...")
    
    # Check and download models if needed
    use_opencv_dnn = download_models_if_needed()
    
    # Initialize components
    database = Database()
    face_recognizer = FaceRecognizer(use_opencv_dnn=use_opencv_dnn)
    attendance_manager = AttendanceManager(database)
    
    # Load existing face recognition model if available
    try:
        face_recognizer.load_encodings()
        print(f"✓ Loaded face recognition model with {len(face_recognizer.encodings)} encodings")
    except Exception as e:
        print(f"Warning: Could not load face encodings: {e}")
        print("Starting with empty model. Please register students first.")
    
    # Initialize GUI
    try:
        print("Starting GUI...")
        app = AttendanceSystemGUI(database, face_recognizer, attendance_manager)
        
        # Start the application
        app.root.mainloop()
    except Exception as e:
        print(f"Error starting application: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up
        print("Cleaning up...")
        database.close()

if __name__ == "__main__":
    main()

