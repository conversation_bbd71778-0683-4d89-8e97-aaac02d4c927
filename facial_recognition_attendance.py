import cv2
import numpy as np
import os
from datetime import datetime
import csv
import threading
import time

# Enable multi-threading in OpenCV
cv2.setNumThreads(4)  # Adjust based on your CPU cores

# Initialize face detector with optimized parameters
face_detector = cv2.FaceDetectorYN.create(
    "face_detection_yunet.onnx",
    "",
    (320, 320),
    0.9,
    0.3,
    5000
)

# Initialize face recognizer
face_recognizer = cv2.FaceRecognizerSF.create(
    "face_recognition_sface.onnx",
    ""
)

# Cache for student features to avoid redundant processing
student_db_cache = {}
recognition_cooldown = {}
COOLDOWN_TIME = 5  # seconds

def load_student_database(database_path):
    # Check if we have a cached version
    if os.path.exists("student_features.npy"):
        try:
            return np.load("student_features.npy", allow_pickle=True).item()
        except:
            print("Failed to load cached features, rebuilding...")
    
    student_db = {}
    for student_id in os.listdir(database_path):
        student_path = os.path.join(database_path, student_id)
        if os.path.isdir(student_path):
            features = []
            for img_file in os.listdir(student_path):
                if img_file.endswith(('.jpg', '.png')):
                    img_path = os.path.join(student_path, img_file)
                    img = cv2.imread(img_path)
                    
                    if img is None:
                        continue
                    
                    # Resize image for faster processing
                    img = cv2.resize(img, (640, 480))
                    
                    # Detect face
                    height, width, _ = img.shape
                    face_detector.setInputSize((width, height))
                    _, faces = face_detector.detect(img)
                    
                    if faces is not None and len(faces) > 0:
                        # Get the first face
                        face = faces[0]
                        # Extract face feature
                        face_align = face_recognizer.alignCrop(img, face)
                        feature = face_recognizer.feature(face_align)
                        features.append(feature)
            
            if features:
                # Average the features for this student
                student_db[student_id] = np.mean(np.array(features), axis=0)
    
    # Cache the features for future use
    np.save("student_features.npy", student_db)
    return student_db

def mark_attendance(student_id):
    # Check cooldown to prevent multiple marks
    current_time = time.time()
    if student_id in recognition_cooldown and current_time - recognition_cooldown[student_id] < COOLDOWN_TIME:
        return
    
    recognition_cooldown[student_id] = current_time
    
    now = datetime.now()
    date = now.strftime("%Y-%m-%d")
    time_str = now.strftime("%H:%M:%S")
    
    # Create attendance directory if it doesn't exist
    os.makedirs("attendance", exist_ok=True)
    
    # Create attendance file if it doesn't exist
    filename = os.path.join("attendance", f"attendance_{date}.csv")
    if not os.path.exists(filename):
        with open(filename, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['Student ID', 'Time'])
    
    # Check if student already marked attendance
    already_marked = False
    try:
        with open(filename, 'r') as f:
            reader = csv.reader(f)
            for row in reader:
                if row and row[0] == student_id:
                    already_marked = True
                    break
    except:
        # File might be empty or corrupted
        pass
    
    # Mark attendance if not already marked
    if not already_marked:
        with open(filename, 'a', newline='') as f:
            writer = csv.writer(f)
            writer.writerow([student_id, time_str])
        print(f"Attendance marked for {student_id}")
    else:
        print(f"{student_id} already marked attendance today")

def process_frame(frame, student_db):
    # Process every other frame to reduce CPU usage
    height, width, _ = frame.shape
    face_detector.setInputSize((width, height))
    _, faces = face_detector.detect(frame)
    
    if faces is not None:
        for face in faces:
            # Draw face rectangle
            x, y, w, h = face[0:4].astype(int)
            cv2.rectangle(frame, (x, y), (x+w, y+h), (0, 255, 0), 2)
            
            # Recognize face
            face_align = face_recognizer.alignCrop(frame, face)
            feature = face_recognizer.feature(face_align)
            
            # Find the closest match
            best_match = None
            best_score = 0
            
            for student_id, db_feature in student_db.items():
                # Calculate cosine similarity
                score = face_recognizer.match(feature, db_feature, cv2.FaceRecognizerSF.FR_COSINE)
                if score > best_score and score > 0.5:  # Threshold
                    best_score = score
                    best_match = student_id
            
            # Display result
            if best_match:
                cv2.putText(frame, f"{best_match} ({best_score:.2f})", 
                            (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                # Mark attendance in a separate thread to avoid blocking
                threading.Thread(target=mark_attendance, args=(best_match,), daemon=True).start()
            else:
                cv2.putText(frame, "Unknown", (x, y-10), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
    
    return frame

def main():
    # Load student database
    print("Loading student database...")
    student_db = load_student_database("students_database")
    print(f"Loaded {len(student_db)} students")
    
    # Start video capture with optimized parameters
    cap = cv2.VideoCapture(0)
    
    # Set camera properties for better performance
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    cap.set(cv2.CAP_PROP_FPS, 30)
    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Minimize buffer size for lower latency
    
    # For performance tracking
    frame_count = 0
    start_time = time.time()
    skip_frame = False
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Process every other frame to reduce CPU load
        if skip_frame:
            skip_frame = False
            # Just display the frame without processing
            cv2.imshow("Attendance System", frame)
        else:
            skip_frame = True
            # Process frame for face detection and recognition
            processed_frame = process_frame(frame, student_db)
            cv2.imshow("Attendance System", processed_frame)
            
            # Calculate and display FPS every 30 frames
            if frame_count % 30 == 0:
                elapsed_time = time.time() - start_time
                fps = frame_count / elapsed_time
                print(f"FPS: {fps:.2f}")
        
        # Exit on 'q' key
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
    
    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()


