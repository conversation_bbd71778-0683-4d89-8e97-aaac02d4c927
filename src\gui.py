import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import cv2
import os
import threading
import time
import platform
import numpy as np
from PIL import Image, ImageTk
from datetime import datetime

class AttendanceSystemGUI:
    def __init__(self, database, face_recognizer, attendance_manager):
        self.database = database
        self.face_recognizer = face_recognizer
        self.attendance_manager = attendance_manager
        
        self.root = tk.Tk()
        self.root.title("Facial Recognition Attendance System")
        self.root.geometry("1200x700")
        self.root.resizable(True, True)
        
        # Platform-specific settings
        self.platform = platform.system()
        self.configure_platform_settings()
        
        # Show loading screen
        self.show_loading_screen()
        
        # Load model in background
        threading.Thread(target=self.load_model_and_setup_ui, daemon=True).start()
        
        # Video capture variables
        self.cap = None
        self.is_capturing = False
        self.current_frame = None
        
        # Registration variables
        self.registration_images = []
        self.max_registration_images = 5
        
        # Recognition cooldown to prevent multiple attendance marks
        self.recognition_cooldown = {}
        self.cooldown_time = 5  # seconds
    
    def configure_platform_settings(self):
        """Configure platform-specific settings"""
        if self.platform == "Windows":
            # Windows-specific settings
            self.camera_api = cv2.CAP_DSHOW  # DirectShow for faster camera startup
            self.root.iconbitmap("assets/icon.ico") if os.path.exists("assets/icon.ico") else None
        elif self.platform == "Darwin":  # macOS
            # macOS-specific settings
            self.camera_api = cv2.CAP_AVFOUNDATION
            # macOS doesn't use iconbitmap
        else:  # Linux and others
            # Linux-specific settings
            self.camera_api = cv2.CAP_V4L2  # Video for Linux
            img = tk.PhotoImage(file="assets/icon.png") if os.path.exists("assets/icon.png") else None
            self.root.tk.call('wm', 'iconphoto', self.root._w, img)
    
    def show_loading_screen(self):
        """Show loading screen while initializing"""
        self.loading_frame = tk.Frame(self.root)
        self.loading_frame.pack(fill=tk.BOTH, expand=True)
        
        loading_label = tk.Label(self.loading_frame, text="Loading Facial Recognition System...", font=("Arial", 18))
        loading_label.pack(pady=20)
        
        # Create a progress bar
        self.progress = ttk.Progressbar(self.loading_frame, orient="horizontal", length=400, mode="indeterminate")
        self.progress.pack(pady=20)
        self.progress.start()
    
    def load_model_and_setup_ui(self):
        """Load face recognition model and set up UI"""
        try:
            self.face_recognizer.load_encodings()
        except Exception as e:
            print(f"Warning: Could not load face encodings: {e}")
        
        # Setup UI on the main thread
        self.root.after(0, self.finish_loading)
    
    def finish_loading(self):
        """Remove loading screen and show the main UI"""
        self.loading_frame.destroy()
        self.setup_ui()
        
        # Bind tab change event to handle camera
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_change)

    def on_tab_change(self, event):
        # Stop camera if it's running when changing tabs
        if self.is_capturing:
            self.stop_camera()

    def stop_camera(self):
        """Centralized method to properly stop the camera"""
        self.is_capturing = False
        if self.cap is not None:
            self.cap.release()
            self.cap = None
        
        # Reset button states
        if hasattr(self, 'start_btn'):
            self.start_btn.config(text="Start Camera")
        if hasattr(self, 'reg_start_btn'):
            self.reg_start_btn.config(text="Start Camera")
        if hasattr(self, 'capture_btn'):
            self.capture_btn.config(state=tk.DISABLED)

    def setup_ui(self):
        # Create notebook (tabs)
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create tabs
        self.attendance_tab = ttk.Frame(self.notebook)
        self.register_tab = ttk.Frame(self.notebook)
        self.reports_tab = ttk.Frame(self.notebook)
        
        self.notebook.add(self.attendance_tab, text="Attendance")
        self.notebook.add(self.register_tab, text="Register Student")
        self.notebook.add(self.reports_tab, text="Reports")
        
        # Add a reset button to the main window
        reset_frame = ttk.Frame(self.root)
        reset_frame.pack(fill=tk.X, padx=10, pady=5)
        
        reset_btn = ttk.Button(reset_frame, text="Reset Face Recognition Model", 
                              command=self.reset_face_model)
        reset_btn.pack(side=tk.RIGHT)
        
        # Setup each tab
        self.setup_attendance_tab()
        self.setup_register_tab()
        self.setup_reports_tab()

    def setup_attendance_tab(self):
        # Left frame for video
        left_frame = ttk.Frame(self.attendance_tab)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Video canvas
        self.attendance_canvas = tk.Canvas(left_frame, width=640, height=480)
        self.attendance_canvas.pack(padx=10, pady=10)
        
        # Control buttons
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.start_btn = ttk.Button(control_frame, text="Start Camera", 
                                   command=self.toggle_attendance_camera)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        self.export_btn = ttk.Button(control_frame, text="Export Today's Attendance", 
                                    command=self.export_today_attendance)
        self.export_btn.pack(side=tk.LEFT, padx=5)
        
        # Right frame for attendance list
        right_frame = ttk.Frame(self.attendance_tab)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, padx=10, pady=10)
        
        # Attendance list
        ttk.Label(right_frame, text="Today's Attendance", font=("Arial", 14)).pack(pady=5)
        
        # Treeview for attendance
        columns = ("ID", "Name", "Time", "Status")
        self.attendance_tree = ttk.Treeview(right_frame, columns=columns, show="headings")
        
        # Set column headings
        for col in columns:
            self.attendance_tree.heading(col, text=col)
            self.attendance_tree.column(col, width=100)
        
        self.attendance_tree.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Refresh button
        refresh_btn = ttk.Button(right_frame, text="Refresh List", 
                                command=self.refresh_attendance_list)
        refresh_btn.pack(pady=5)

    def setup_register_tab(self):
        # Left frame for video and capture
        left_frame = ttk.Frame(self.register_tab)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Video canvas
        self.register_canvas = tk.Canvas(left_frame, width=640, height=480)
        self.register_canvas.pack(padx=10, pady=10)
        
        # Control buttons
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.reg_start_btn = ttk.Button(control_frame, text="Start Camera", 
                                      command=self.toggle_register_camera)
        self.reg_start_btn.pack(side=tk.LEFT, padx=5)
        
        self.capture_btn = ttk.Button(control_frame, text="Capture Image", 
                                     command=self.capture_image)
        self.capture_btn.pack(side=tk.LEFT, padx=5)
        self.capture_btn.config(state=tk.DISABLED)
        
        # Right frame for student info
        right_frame = ttk.Frame(self.register_tab)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, padx=10, pady=10)
        
        # Student info form
        ttk.Label(right_frame, text="Student Registration", font=("Arial", 14)).pack(pady=5)
        
        form_frame = ttk.Frame(right_frame)
        form_frame.pack(fill=tk.X, pady=10)
        
        # Student ID
        ttk.Label(form_frame, text="Student ID:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.student_id_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.student_id_var).grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # Search button for existing students
        search_btn = ttk.Button(form_frame, text="Search", command=self.search_student)
        search_btn.grid(row=0, column=2, padx=5, pady=5)
        
        # Name
        ttk.Label(form_frame, text="Name:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.name_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.name_var).grid(row=1, column=1, sticky=tk.W, pady=5)
        
        # Course
        ttk.Label(form_frame, text="Course:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.course_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.course_var).grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # Email
        ttk.Label(form_frame, text="Email:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.email_var = tk.StringVar()
        ttk.Entry(form_frame, textvariable=self.email_var).grid(row=3, column=1, sticky=tk.W, pady=5)
        
        # Images captured label
        self.images_label = ttk.Label(form_frame, text="Images: 0/5")
        self.images_label.grid(row=4, column=0, columnspan=2, pady=5)
        
        # Button frame
        btn_frame = ttk.Frame(form_frame)
        btn_frame.grid(row=5, column=0, columnspan=3, pady=10)
        
        # Register button
        self.register_btn = ttk.Button(btn_frame, text="Register New", command=self.register_student)
        self.register_btn.pack(side=tk.LEFT, padx=5)
        
        # Update button
        self.update_btn = ttk.Button(btn_frame, text="Update Student", command=self.update_student)
        self.update_btn.pack(side=tk.LEFT, padx=5)
        self.update_btn.config(state=tk.DISABLED)

    def setup_reports_tab(self):
        # Main frame
        main_frame = ttk.Frame(self.reports_tab)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Monthly report section
        report_frame = ttk.LabelFrame(main_frame, text="Generate Monthly Report")
        report_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Year and month selection
        selection_frame = ttk.Frame(report_frame)
        selection_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Year
        ttk.Label(selection_frame, text="Year:").grid(row=0, column=0, padx=5, pady=5)
        current_year = datetime.now().year
        self.year_var = tk.StringVar(value=str(current_year))
        year_combo = ttk.Combobox(selection_frame, textvariable=self.year_var, 
                                 values=[str(y) for y in range(current_year-5, current_year+1)])
        year_combo.grid(row=0, column=1, padx=5, pady=5)
        
        # Month
        ttk.Label(selection_frame, text="Month:").grid(row=0, column=2, padx=5, pady=5)
        current_month = datetime.now().month
        self.month_var = tk.StringVar(value=str(current_month))
        month_combo = ttk.Combobox(selection_frame, textvariable=self.month_var,
                                  values=[str(m) for m in range(1, 13)])
        month_combo.grid(row=0, column=3, padx=5, pady=5)
        
        # Generate button
        generate_btn = ttk.Button(selection_frame, text="Generate Report", 
                                 command=self.generate_monthly_report)
        generate_btn.grid(row=0, column=4, padx=20, pady=5)
        
        # Recent reports section
        recent_frame = ttk.LabelFrame(main_frame, text="Recent Reports")
        recent_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # List of reports
        self.reports_list = tk.Listbox(recent_frame, height=10)
        self.reports_list.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Open report button
        open_btn = ttk.Button(recent_frame, text="Open Selected Report", 
                             command=self.open_report)
        open_btn.pack(pady=5)
        
        # Refresh list of reports
        self.refresh_reports_list()

    def toggle_attendance_camera(self):
        if self.is_capturing:
            # Stop camera
            self.is_capturing = False
            if self.cap is not None:
                self.cap.release()
                self.cap = None
            self.start_btn.config(text="Start Camera")
        else:
            # Show loading indicator
            self.start_btn.config(text="Starting...", state=tk.DISABLED)
            self.attendance_canvas.delete("all")
            self.attendance_canvas.create_text(320, 240, text="Initializing camera...", font=("Arial", 14))
            self.root.update()
            
            # Start camera in a separate thread
            threading.Thread(target=self._start_attendance_camera, daemon=True).start()

    def _start_attendance_camera(self):
        # Initialize camera
        try:
            self.cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)  # Use DirectShow on Windows for faster startup
            
            # Set camera properties for faster startup
            if self.cap.isOpened():
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Minimize buffer size
                self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                self.cap.set(cv2.CAP_PROP_FPS, 30)  # Set FPS explicitly
                
                # Read just one frame to warm up the camera
                ret, _ = self.cap.read()
                if not ret:
                    raise Exception("Failed to read from camera")
                
                # Update UI in main thread
                self.root.after(0, lambda: self._finish_camera_start())
            else:
                raise Exception("Camera failed to open")
        except Exception as e:
            print(f"Camera error: {e}")
            # Update UI in main thread if camera failed
            self.root.after(0, lambda: self._camera_start_failed(str(e)))

    def _finish_camera_start(self):
        self.is_capturing = True
        self.start_btn.config(text="Stop Camera", state=tk.NORMAL)
        self.update_attendance_frame()

    def _camera_start_failed(self, error_msg="Could not open camera"):
        self.start_btn.config(text="Start Camera", state=tk.NORMAL)
        tk.messagebox.showerror("Camera Error", error_msg)

    def update_attendance_frame(self):
        if not self.is_capturing:
            return
        
        ret, frame = self.cap.read()
        if ret:
            # Process frame for face recognition more frequently
            if hasattr(self, 'frame_count'):
                self.frame_count += 1
            else:
                self.frame_count = 0
            
            # Process face recognition every 5 frames for more stability
            if self.frame_count % 5 == 0:
                # Much stricter threshold values
                threshold = getattr(self, 'threshold_var', tk.DoubleVar(value=0.35)).get()  # Tightened from 0.4
                
                # Process frame for face recognition with much higher confidence requirement
                faces = self.face_recognizer.identify_face(frame, tolerance=threshold, min_confidence=0.8)  # Tightened from 0.7
                
                # Draw rectangles around faces and mark attendance
                for result in faces:
                    if len(result) == 3:  # New format with confidence
                        name, (top, right, bottom, left), confidence = result
                    else:  # Old format without confidence
                        name, (top, right, bottom, left) = result
                        confidence = 0
                    
                    # Only proceed if confidence is high enough
                    if name != "Unknown" and confidence < 0.75:  # Additional confidence check
                        name = "Unknown"
                    
                    # Draw rectangle
                    color = (0, 255, 0) if name != "Unknown" else (0, 0, 255)
                    cv2.rectangle(frame, (left, top), (right, bottom), color, 2)
                    
                    # Put name and confidence text
                    label = f"{name} ({confidence:.2f})" if name != "Unknown" else "Unknown"
                    cv2.putText(frame, label, (left, top - 10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                    
                    # Mark attendance if not Unknown and not in cooldown
                    # Increased cooldown time to 5 seconds for more stability
                    if name != "Unknown" and (name not in self.recognition_cooldown or 
                                             time.time() - self.recognition_cooldown[name] > 5):
                        success = self.attendance_manager.mark_attendance(name)
                        if success:
                            self.recognition_cooldown[name] = time.time()
                            # Use after method to update UI to avoid blocking the main thread
                            self.root.after(10, self.refresh_attendance_list)
        
        # Convert to tkinter format
        cv2image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        img = Image.fromarray(cv2image)
        imgtk = ImageTk.PhotoImage(image=img)
        
        # Update canvas
        self.attendance_canvas.create_image(0, 0, anchor=tk.NW, image=imgtk)
        self.attendance_canvas.image = imgtk

        # Schedule next update with a slightly longer delay for smoother performance
        self.attendance_canvas.after(30, self.update_attendance_frame)

    def export_today_attendance(self):
        path = self.attendance_manager.export_today_attendance()
        if path:
            tk.messagebox.showinfo("Export Successful", f"Attendance exported to {path}")
        else:
            tk.messagebox.showerror("Export Failed", "Failed to export attendance")

    def refresh_attendance_list(self):
        # Clear current list
        for item in self.attendance_tree.get_children():
            self.attendance_tree.delete(item)
        
        # Get today's attendance
        records = self.attendance_manager.get_today_attendance()
        
        # Insert into treeview
        for record in records:
            self.attendance_tree.insert("", tk.END, values=record)

    def toggle_register_camera(self):
        if self.is_capturing:
            # Stop camera
            self.is_capturing = False
            if self.cap is not None:
                self.cap.release()
                self.cap = None
            self.reg_start_btn.config(text="Start Camera")
            self.capture_btn.config(state=tk.DISABLED)
        else:
            # Show loading indicator
            self.reg_start_btn.config(text="Starting...", state=tk.DISABLED)
            self.register_canvas.delete("all")
            self.register_canvas.create_text(320, 240, text="Initializing camera...", font=("Arial", 14))
            self.root.update()
            
            # Start camera in a separate thread
            threading.Thread(target=self._start_register_camera, daemon=True).start()

    def _start_register_camera(self):
        # Initialize camera
        try:
            self.cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)  # Use DirectShow on Windows for faster startup
            
            # Set camera properties for faster startup
            if self.cap.isOpened():
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Minimize buffer size
                self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                self.cap.set(cv2.CAP_PROP_FPS, 30)  # Set FPS explicitly
                
                # Read just one frame to warm up the camera
                ret, _ = self.cap.read()
                if not ret:
                    raise Exception("Failed to read from camera")
                
                # Update UI in main thread
                self.root.after(0, lambda: self._finish_register_camera_start())
            else:
                raise Exception("Camera failed to open")
        except Exception as e:
            print(f"Camera error: {e}")
            # Update UI in main thread if camera failed
            self.root.after(0, lambda: self._register_camera_start_failed(str(e)))

    def _finish_register_camera_start(self):
        self.is_capturing = True
        self.reg_start_btn.config(text="Stop Camera", state=tk.NORMAL)
        self.capture_btn.config(state=tk.NORMAL)
        self.update_register_frame()

    def _register_camera_start_failed(self):
        self.reg_start_btn.config(text="Start Camera", state=tk.NORMAL)
        tk.messagebox.showerror("Error", "Could not open camera")

    def update_register_frame(self):
        if not self.is_capturing:
            return
        
        ret, frame = self.cap.read()
        if ret:
            # Store current frame for capture
            self.current_frame = frame.copy()
            
            # Convert to tkinter format
            cv2image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            img = Image.fromarray(cv2image)
            imgtk = ImageTk.PhotoImage(image=img)
            
            # Update canvas
            self.register_canvas.create_image(0, 0, anchor=tk.NW, image=imgtk)
            self.register_canvas.image = imgtk
        
        # Schedule next update with a slightly longer delay
        self.register_canvas.after(30, self.update_register_frame)

    def capture_image(self):
        if self.current_frame is not None and len(self.registration_images) < self.max_registration_images:
            self.registration_images.append(self.current_frame.copy())
            self.images_label.config(text=f"Images: {len(self.registration_images)}/{self.max_registration_images}")
            
            if len(self.registration_images) >= self.max_registration_images:
                self.capture_btn.config(state=tk.DISABLED)

    def register_student(self):
        # Get form data
        student_id = self.student_id_var.get().strip()
        name = self.name_var.get().strip()
        course = self.course_var.get().strip()
        email = self.email_var.get().strip()
        
        # Validate
        if not student_id or not name:
            tk.messagebox.showerror("Error", "Student ID and Name are required")
            return
        
        if len(self.registration_images) == 0:
            tk.messagebox.showerror("Error", "Please capture at least one face image")
            return
        
        # Add student to database
        success = self.database.add_student(student_id, name, course, email)
        if not success:
            tk.messagebox.showerror("Error", "Student ID already exists")
            return
        
        # Register face
        face_success = self.face_recognizer.register_new_student(student_id, self.registration_images)
        if not face_success:
            tk.messagebox.showwarning("Warning", "Student added but face registration failed")
        else:
            tk.messagebox.showinfo("Success", "Student registered successfully")
        
        # Clear form and images
        self.student_id_var.set("")
        self.name_var.set("")
        self.course_var.set("")
        self.email_var.set("")
        self.registration_images = []
        self.images_label.config(text=f"Images: 0/{self.max_registration_images}")
        self.capture_btn.config(state=tk.NORMAL)

    def generate_monthly_report(self):
        try:
            year = int(self.year_var.get())
            month = int(self.month_var.get())
            
            if month < 1 or month > 12:
                tk.messagebox.showerror("Error", "Month must be between 1 and 12")
                return
            
            path = self.attendance_manager.export_monthly_report(year, month)
            if path:
                tk.messagebox.showinfo("Report Generated", f"Report saved to {path}")
                self.refresh_reports_list()
            else:
                tk.messagebox.showerror("Error", "Failed to generate report")
        except ValueError:
            tk.messagebox.showerror("Error", "Please enter valid year and month")

    def refresh_reports_list(self):
        """Refresh the reports list"""
        # Clear current list
        self.reports_list.delete(0, tk.END)
        
        # Get all report files
        if os.path.exists(self.attendance_manager.output_dir):
            files = os.listdir(self.attendance_manager.output_dir)
            report_files = [f for f in files if f.endswith('.csv')]
            
            # Add to listbox
            for file in sorted(report_files):
                self.reports_list.insert(tk.END, file)

    def open_report(self):
        # Get selected report
        selection = self.reports_list.curselection()
        if not selection:
            tk.messagebox.showinfo("Info", "Please select a report to open")
            return
        
        report_name = self.reports_list.get(selection[0])
        report_path = os.path.join(self.attendance_manager.output_dir, report_name)
        
        # Open with default application
        try:
            import subprocess
            import platform
            
            if platform.system() == 'Windows':
                os.startfile(report_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(('open', report_path))
            else:  # Linux
                subprocess.call(('xdg-open', report_path))
        except Exception as e:
            tk.messagebox.showerror("Error", f"Could not open file: {str(e)}")

    def reset_face_model(self):
        """Reset the face recognition model by clearing all encodings"""
        if tk.messagebox.askyesno("Reset Model", "Are you sure you want to reset the face recognition model? This will delete all face data."):
            self.face_recognizer.encodings = []
            self.face_recognizer.names = []
            self.face_recognizer.save_encodings()
            tk.messagebox.showinfo("Reset Complete", "Face recognition model has been reset")

    def search_student(self):
        """Search for a student by ID and populate the form if found"""
        student_id = self.student_id_var.get().strip()
        if not student_id:
            tk.messagebox.showerror("Error", "Please enter a Student ID to search")
            return
        
        student = self.database.get_student(student_id)
        if student:
            # Populate form with student data
            self.name_var.set(student[1])
            self.course_var.set(student[2])
            self.email_var.set(student[3])
            
            # Enable update button, disable registration button
            self.update_btn.config(state=tk.NORMAL)
            self.register_btn.config(state=tk.DISABLED)
            
            # Show message
            tk.messagebox.showinfo("Student Found", f"Student {student[1]} found. You can update their information.")
        else:
            # Clear form and enable registration button
            self.name_var.set("")
            self.course_var.set("")
            self.email_var.set("")
            
            self.update_btn.config(state=tk.DISABLED)
            self.register_btn.config(state=tk.NORMAL)
            
            tk.messagebox.showinfo("Student Not Found", "No student found with this ID. You can register a new student.")

    def update_student(self):
        """Update an existing student's information"""
        student_id = self.student_id_var.get().strip()
        name = self.name_var.get().strip()
        course = self.course_var.get().strip()
        email = self.email_var.get().strip()
        
        # Validate required fields
        if not student_id or not name:
            tk.messagebox.showerror("Error", "Student ID and Name are required")
            return
        
        # Update student in database
        success = self.database.update_student(student_id, name, course, email)
        if success:
            tk.messagebox.showinfo("Success", "Student information updated successfully")
            
            # Reset form state
            self.update_btn.config(state=tk.DISABLED)
            self.register_btn.config(state=tk.NORMAL)
        else:
            tk.messagebox.showerror("Error", "Failed to update student information")







